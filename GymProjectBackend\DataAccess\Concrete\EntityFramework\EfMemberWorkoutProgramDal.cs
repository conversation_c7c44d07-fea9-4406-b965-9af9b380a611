using Core.DataAccess.EntityFramework;
using Core.Utilities.Results;
using Core.Utilities.Security.CompanyContext;
using DataAccess.Abstract;
using DataAccess.Concrete.EntityFramework;
using Entities.Concrete;
using Entities.DTOs;
using System;
using System.Collections.Generic;
using System.Linq;

namespace DataAccess.Concrete.EntityFramework
{
    public class EfMemberWorkoutProgramDal : EfCompanyEntityRepositoryBase<MemberWorkoutProgram, GymContext>, IMemberWorkoutProgramDal
    {
        private readonly ICompanyContext _companyContext;

        // Constructor injection (Scalability için)
        public EfMemberWorkoutProgramDal(ICompanyContext companyContext, GymContext context) : base(companyContext, context)
        {
            _companyContext = companyContext;
        }

        // Backward compatibility constructor
        public EfMemberWorkoutProgramDal(ICompanyContext companyContext) : base(companyContext)
        {
            _companyContext = companyContext;
        }
        public List<MemberWorkoutProgramListDto> GetCompanyAssignments()
        {
            // Güvenli CompanyID al - JWT token'dan
            int companyId = _companyContext.GetCompanyId();

            if (companyId <= 0)
            {
                return new List<MemberWorkoutProgramListDto>();
            }

            var result = from mwp in _context.MemberWorkoutPrograms
                         join m in _context.Members on mwp.MemberID equals m.MemberID
                         join wpt in _context.WorkoutProgramTemplates on mwp.WorkoutProgramTemplateID equals wpt.WorkoutProgramTemplateID
                         where mwp.CompanyID == companyId && mwp.IsActive == true
                         && m.IsActive == true // Member'ın aktif olması kontrolü eklendi
                         && m.CompanyID == companyId // Üyenin de aynı şirkete ait olduğundan emin ol
                         && wpt.CompanyID == companyId // Template'in de aynı şirkete ait olduğundan emin ol
                         orderby mwp.CreationDate descending
                         select new MemberWorkoutProgramListDto
                         {
                             MemberWorkoutProgramID = mwp.MemberWorkoutProgramID,
                             MemberID = mwp.MemberID,
                             MemberName = m.Name,
                             ProgramName = wpt.ProgramName,
                             ExperienceLevel = wpt.ExperienceLevel,
                             TargetGoal = wpt.TargetGoal,
                             StartDate = mwp.StartDate,
                             EndDate = mwp.EndDate,
                             IsActive = mwp.IsActive,
                             // Gün sayısını hesapla
                             DayCount = _context.WorkoutProgramDays
                                 .Where(wpd => wpd.WorkoutProgramTemplateID == wpt.WorkoutProgramTemplateID)
                                 .Count(),
                             // Egzersiz sayısını hesapla
                             ExerciseCount = (from wpd in _context.WorkoutProgramDays
                                             join wpe in _context.WorkoutProgramExercises on wpd.WorkoutProgramDayID equals wpe.WorkoutProgramDayID
                                             where wpd.WorkoutProgramTemplateID == wpt.WorkoutProgramTemplateID
                                             select wpe.WorkoutProgramExerciseID).Count()
                         };

            return result.ToList();
        }

        public List<MemberWorkoutProgramDto> GetMemberActivePrograms(int memberId)
        {
            var result = from mwp in _context.MemberWorkoutPrograms
                         join m in _context.Members on mwp.MemberID equals m.MemberID
                         join wpt in _context.WorkoutProgramTemplates on mwp.WorkoutProgramTemplateID equals wpt.WorkoutProgramTemplateID
                         where mwp.MemberID == memberId && mwp.IsActive == true
                         && m.IsActive == true // Member'ın aktif olması kontrolü eklendi
                         orderby mwp.StartDate descending
                         select new MemberWorkoutProgramDto
                         {
                             MemberWorkoutProgramID = mwp.MemberWorkoutProgramID,
                             MemberID = mwp.MemberID,
                             MemberName = m.Name,
                             MemberPhone = m.PhoneNumber,
                             WorkoutProgramTemplateID = mwp.WorkoutProgramTemplateID,
                             ProgramName = wpt.ProgramName,
                             ProgramDescription = wpt.Description,
                             ExperienceLevel = wpt.ExperienceLevel,
                             TargetGoal = wpt.TargetGoal,
                             CompanyID = mwp.CompanyID,
                             StartDate = mwp.StartDate,
                             EndDate = mwp.EndDate,
                             Notes = mwp.Notes,
                             IsActive = mwp.IsActive,
                             CreationDate = mwp.CreationDate,
                             // Gün sayısını hesapla
                             DayCount = _context.WorkoutProgramDays
                                 .Where(wpd => wpd.WorkoutProgramTemplateID == wpt.WorkoutProgramTemplateID)
                                 .Count(),
                             // Egzersiz sayısını hesapla
                             ExerciseCount = (from wpd in _context.WorkoutProgramDays
                                             join wpe in _context.WorkoutProgramExercises on wpd.WorkoutProgramDayID equals wpe.WorkoutProgramDayID
                                             where wpd.WorkoutProgramTemplateID == wpt.WorkoutProgramTemplateID
                                             select wpe.WorkoutProgramExerciseID).Count()
                         };

            return result.ToList();
        }

        public List<MemberWorkoutProgramHistoryDto> GetMemberProgramHistory(int memberId)
        {
            var result = from mwp in _context.MemberWorkoutPrograms
                         join m in _context.Members on mwp.MemberID equals m.MemberID
                         join wpt in _context.WorkoutProgramTemplates on mwp.WorkoutProgramTemplateID equals wpt.WorkoutProgramTemplateID
                         where mwp.MemberID == memberId
                         && m.IsActive == true // Member'ın aktif olması kontrolü eklendi
                         orderby mwp.CreationDate descending
                         select new MemberWorkoutProgramHistoryDto
                         {
                             MemberWorkoutProgramID = mwp.MemberWorkoutProgramID,
                             ProgramName = wpt.ProgramName,
                             StartDate = mwp.StartDate,
                             EndDate = mwp.EndDate,
                             IsActive = mwp.IsActive,
                             Notes = mwp.Notes,
                             CreationDate = mwp.CreationDate
                         };

            return result.ToList();
        }

        public List<MemberActiveWorkoutProgramDto> GetActiveWorkoutProgramsByUserId(int userId)
        {
            var result = from mwp in _context.MemberWorkoutPrograms
                         join m in _context.Members on mwp.MemberID equals m.MemberID
                         join wpt in _context.WorkoutProgramTemplates on mwp.WorkoutProgramTemplateID equals wpt.WorkoutProgramTemplateID
                         where m.UserID == userId && mwp.IsActive == true && m.IsActive == true
                               && mwp.CompanyID == m.CompanyID && wpt.CompanyID == m.CompanyID
                         orderby mwp.StartDate descending
                         select new MemberActiveWorkoutProgramDto
                         {
                             MemberWorkoutProgramID = mwp.MemberWorkoutProgramID,
                             WorkoutProgramTemplateID = mwp.WorkoutProgramTemplateID,
                             ProgramName = wpt.ProgramName,
                             ProgramDescription = wpt.Description,
                             ExperienceLevel = wpt.ExperienceLevel,
                             TargetGoal = wpt.TargetGoal,
                             StartDate = mwp.StartDate,
                             EndDate = mwp.EndDate,
                             Notes = mwp.Notes,
                             // Gün sayısını hesapla
                             DayCount = _context.WorkoutProgramDays
                                 .Where(wpd => wpd.WorkoutProgramTemplateID == wpt.WorkoutProgramTemplateID)
                                 .Count(),
                             // Egzersiz sayısını hesapla
                             ExerciseCount = (from wpd in _context.WorkoutProgramDays
                                             join wpe in _context.WorkoutProgramExercises on wpd.WorkoutProgramDayID equals wpe.WorkoutProgramDayID
                                             where wpd.WorkoutProgramTemplateID == wpt.WorkoutProgramTemplateID
                                             select wpe.WorkoutProgramExerciseID).Count()
                         };

            return result.ToList();
        }

        public MemberWorkoutProgramDto GetAssignmentDetail(int assignmentId)
        {
            var result = from mwp in _context.MemberWorkoutPrograms
                         join m in _context.Members on mwp.MemberID equals m.MemberID
                         join wpt in _context.WorkoutProgramTemplates on mwp.WorkoutProgramTemplateID equals wpt.WorkoutProgramTemplateID
                         where mwp.MemberWorkoutProgramID == assignmentId
                         select new MemberWorkoutProgramDto
                         {
                             MemberWorkoutProgramID = mwp.MemberWorkoutProgramID,
                             MemberID = mwp.MemberID,
                             MemberName = m.Name,
                             MemberPhone = m.PhoneNumber,
                             WorkoutProgramTemplateID = mwp.WorkoutProgramTemplateID,
                             ProgramName = wpt.ProgramName,
                             ProgramDescription = wpt.Description,
                             ExperienceLevel = wpt.ExperienceLevel,
                             TargetGoal = wpt.TargetGoal,
                             CompanyID = mwp.CompanyID,
                             StartDate = mwp.StartDate,
                             EndDate = mwp.EndDate,
                             Notes = mwp.Notes,
                             IsActive = mwp.IsActive,
                             CreationDate = mwp.CreationDate
                         };

            return result.FirstOrDefault();
        }

        public int GetAssignedMemberCount(int workoutProgramTemplateId)
        {
            return _context.MemberWorkoutPrograms
                .Where(mwp => mwp.WorkoutProgramTemplateID == workoutProgramTemplateId && mwp.IsActive == true)
                .Count();
        }

        public int GetActiveAssignmentCount(int companyId)
        {
            return _context.MemberWorkoutPrograms
                .Where(mwp => mwp.CompanyID == companyId && mwp.IsActive == true)
                .Count();
        }

        public MemberWorkoutProgramDetailDto GetProgramDetailByUser(int userId, int memberWorkoutProgramId)
        {
            // Önce üyenin bu programa erişim yetkisi var mı kontrol et
            var assignment = (from mwp in _context.MemberWorkoutPrograms
                             join m in _context.Members on mwp.MemberID equals m.MemberID
                             join wpt in _context.WorkoutProgramTemplates on mwp.WorkoutProgramTemplateID equals wpt.WorkoutProgramTemplateID
                             where m.UserID == userId && mwp.MemberWorkoutProgramID == memberWorkoutProgramId && mwp.IsActive == true && m.IsActive == true
                                   && mwp.CompanyID == m.CompanyID && wpt.CompanyID == m.CompanyID
                             select new MemberWorkoutProgramDetailDto
                             {
                                 MemberWorkoutProgramID = mwp.MemberWorkoutProgramID,
                                 MemberID = mwp.MemberID,
                                 MemberName = m.Name,
                                 WorkoutProgramTemplateID = mwp.WorkoutProgramTemplateID,
                                 ProgramName = wpt.ProgramName,
                                 ProgramDescription = wpt.Description,
                                 ExperienceLevel = wpt.ExperienceLevel,
                                 TargetGoal = wpt.TargetGoal,
                                 StartDate = mwp.StartDate,
                                 EndDate = mwp.EndDate,
                                 Notes = mwp.Notes,
                                 IsActive = mwp.IsActive,
                                 // Gün sayısını hesapla
                                 DayCount = _context.WorkoutProgramDays
                                     .Where(wpd => wpd.WorkoutProgramTemplateID == wpt.WorkoutProgramTemplateID)
                                     .Count(),
                                 // Egzersiz sayısını hesapla
                                 ExerciseCount = (from wpd in _context.WorkoutProgramDays
                                                 join wpe in _context.WorkoutProgramExercises on wpd.WorkoutProgramDayID equals wpe.WorkoutProgramDayID
                                                 where wpd.WorkoutProgramTemplateID == wpt.WorkoutProgramTemplateID
                                                 select wpe.WorkoutProgramExerciseID).Count()
                             }).FirstOrDefault();

            if (assignment != null)
            {
                // Program günlerini ve egzersizlerini getir
                assignment.Days = GetWorkoutProgramDaysWithExercises(_context, assignment.WorkoutProgramTemplateID);
            }

            return assignment;
        }

        /// <summary>
        /// Program günlerini egzersizleriyle birlikte getirir
        /// </summary>
        private List<WorkoutProgramDayDto> GetWorkoutProgramDaysWithExercises(GymContext context, int workoutProgramTemplateId)
        {
            var days = (from wpd in context.WorkoutProgramDays
                       where wpd.WorkoutProgramTemplateID == workoutProgramTemplateId
                       orderby wpd.DayNumber
                       select new WorkoutProgramDayDto
                       {
                           WorkoutProgramDayID = wpd.WorkoutProgramDayID,
                           WorkoutProgramTemplateID = wpd.WorkoutProgramTemplateID,
                           DayNumber = wpd.DayNumber,
                           DayName = wpd.DayName,
                           IsRestDay = wpd.IsRestDay,
                           CreationDate = wpd.CreationDate
                       }).ToList();

            // Her gün için egzersizleri getir
            foreach (var day in days)
            {
                day.Exercises = GetWorkoutProgramExercises(context, day.WorkoutProgramDayID);
            }

            return days;
        }

        /// <summary>
        /// Belirli bir günün egzersizlerini getirir
        /// </summary>
        private List<WorkoutProgramExerciseDto> GetWorkoutProgramExercises(GymContext context, int workoutProgramDayId)
        {
            var exercises = (from wpe in context.WorkoutProgramExercises
                           where wpe.WorkoutProgramDayID == workoutProgramDayId
                           orderby wpe.OrderIndex
                           select new WorkoutProgramExerciseDto
                           {
                               WorkoutProgramExerciseID = wpe.WorkoutProgramExerciseID,
                               WorkoutProgramDayID = wpe.WorkoutProgramDayID,
                               ExerciseType = wpe.ExerciseType,
                               ExerciseID = wpe.ExerciseID,
                               OrderIndex = wpe.OrderIndex,
                               Sets = wpe.Sets,
                               Reps = wpe.Reps,
                               RestTime = wpe.RestTime,
                               Notes = wpe.Notes,
                               CreationDate = wpe.CreationDate
                           }).ToList();

            // Egzersiz bilgilerini join et
            foreach (var exercise in exercises)
            {
                if (exercise.ExerciseType == "System")
                {
                    var systemExercise = (from e in context.SystemExercises
                                        join ec in context.ExerciseCategories on e.ExerciseCategoryID equals ec.ExerciseCategoryID
                                        where e.SystemExerciseID == exercise.ExerciseID
                                        select new { e.ExerciseName, e.Description, ec.CategoryName }).FirstOrDefault();

                    if (systemExercise != null)
                    {
                        exercise.ExerciseName = systemExercise.ExerciseName;
                        exercise.ExerciseDescription = systemExercise.Description;
                        exercise.CategoryName = systemExercise.CategoryName;
                    }
                }
                else if (exercise.ExerciseType == "Company")
                {
                    var companyExercise = (from ce in context.CompanyExercises
                                         join ec in context.ExerciseCategories on ce.ExerciseCategoryID equals ec.ExerciseCategoryID
                                         where ce.CompanyExerciseID == exercise.ExerciseID
                                         select new { ce.ExerciseName, ce.Description, ec.CategoryName }).FirstOrDefault();

                    if (companyExercise != null)
                    {
                        exercise.ExerciseName = companyExercise.ExerciseName;
                        exercise.ExerciseDescription = companyExercise.Description;
                        exercise.CategoryName = companyExercise.CategoryName;
                    }
                }
            }

            return exercises;
        }

        /// <summary>
        /// SOLID prensiplerine uygun: Complex business logic DAL katmanında
        /// </summary>
        public IResult AssignProgramWithValidation(MemberWorkoutProgramAddDto assignmentDto, int companyId)
        {
            try
            {
                // Validation logic
                if (!CheckIfMemberExists(assignmentDto.MemberID))
                    return new ErrorResult("Üye bulunamadı");

                if (!CheckIfProgramExists(assignmentDto.WorkoutProgramTemplateID))
                    return new ErrorResult("Program şablonu bulunamadı");

                if (!CheckIfMemberBelongsToCompany(assignmentDto.MemberID, companyId))
                    return new ErrorResult("Üye bu şirkete ait değil");

                if (!CheckIfProgramBelongsToCompany(assignmentDto.WorkoutProgramTemplateID, companyId))
                    return new ErrorResult("Program şablonu bu şirkete ait değil");

                if (CheckIfMemberHasActiveProgramAssignment(assignmentDto.MemberID, assignmentDto.WorkoutProgramTemplateID))
                    return new ErrorResult("Üyenin bu programa aktif ataması zaten mevcut");

                if (!CheckDateRange(assignmentDto.StartDate, assignmentDto.EndDate))
                    return new ErrorResult("Geçersiz tarih aralığı");

                // Business logic - Assignment creation
                var assignment = new MemberWorkoutProgram
                {
                    MemberID = assignmentDto.MemberID,
                    WorkoutProgramTemplateID = assignmentDto.WorkoutProgramTemplateID,
                    CompanyID = companyId,
                    StartDate = assignmentDto.StartDate,
                    EndDate = assignmentDto.EndDate,
                    Notes = assignmentDto.Notes,
                    IsActive = true,
                    CreationDate = DateTime.Now
                };

                if (_context != null)
                {
                    // DI kullanılıyor - Scalability optimized
                    _context.MemberWorkoutPrograms.Add(assignment);
                    _context.SaveChanges();
                }
                else
                {
                    // Backward compatibility
                    using (var context = new GymContext())
                    {
                        context.MemberWorkoutPrograms.Add(assignment);
                        context.SaveChanges();
                    }
                }

                return new SuccessResult("Program başarıyla atandı");
            }
            catch (Exception ex)
            {
                return new ErrorResult($"Program atanırken hata oluştu: {ex.Message}");
            }
        }

        // Helper validation methods
        private bool CheckIfMemberExists(int memberId)
        {
            if (_context != null)
                return _context.Members.Any(m => m.MemberID == memberId && m.IsActive == true);

            using (var context = new GymContext())
                return context.Members.Any(m => m.MemberID == memberId && m.IsActive == true);
        }

        private bool CheckIfProgramExists(int programId)
        {
            if (_context != null)
                return _context.WorkoutProgramTemplates.Any(p => p.WorkoutProgramTemplateID == programId && p.IsActive == true);

            using (var context = new GymContext())
                return context.WorkoutProgramTemplates.Any(p => p.WorkoutProgramTemplateID == programId && p.IsActive == true);
        }

        private bool CheckIfMemberBelongsToCompany(int memberId, int companyId)
        {
            if (_context != null)
                return _context.Members.Any(m => m.MemberID == memberId && m.CompanyID == companyId);

            using (var context = new GymContext())
                return context.Members.Any(m => m.MemberID == memberId && m.CompanyID == companyId);
        }

        private bool CheckIfProgramBelongsToCompany(int programId, int companyId)
        {
            if (_context != null)
                return _context.WorkoutProgramTemplates.Any(p => p.WorkoutProgramTemplateID == programId && p.CompanyID == companyId);

            using (var context = new GymContext())
                return context.WorkoutProgramTemplates.Any(p => p.WorkoutProgramTemplateID == programId && p.CompanyID == companyId);
        }

        private bool CheckIfMemberHasActiveProgramAssignment(int memberId, int programId)
        {
            if (_context != null)
                return _context.MemberWorkoutPrograms.Any(mwp => mwp.MemberID == memberId && mwp.WorkoutProgramTemplateID == programId && mwp.IsActive == true);

            using (var context = new GymContext())
                return context.MemberWorkoutPrograms.Any(mwp => mwp.MemberID == memberId && mwp.WorkoutProgramTemplateID == programId && mwp.IsActive == true);
        }

        private bool CheckDateRange(DateTime? startDate, DateTime? endDate)
        {
            if (!startDate.HasValue || !endDate.HasValue)
                return false;

            return startDate.Value < endDate.Value && startDate.Value >= DateTime.Today;
        }
    }
}
